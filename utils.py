import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()

async def check_ban(uid):
    api_url = f"https://check-ban.onrender.com/check_ban/{uid}"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url) as response:
                if response.status != 200:
                    return None

                response_data = await response.json()
                if response_data.get("status") == 200:
                    data = response_data.get("data")

                    return {
                        "is_banned": data.get("is_banned", 0),
                        "nickname": data.get("nickname", ""),
                        "period": data.get("period", 0),
                        "region": data.get('region',0 )
                    }
                else:
                    return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

async def test_endpoints(uid, region="india"):
    """Test multiple potential API endpoints with a specific UID and region."""
    endpoints = [
        f"https://check-ban.onrender.com/check_ban/{uid}",
        f"https://check-ban.onrender.com/player/{uid}",
        f"https://check-ban.onrender.com/stats/{uid}",
        f"https://check-ban.onrender.com/matches/{uid}",
        f"https://check-ban.onrender.com/achievements/{uid}"
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints:
            try:
                async with session.get(endpoint) as response:
                    status = response.status
                    if status == 200:
                        data = await response.json()
                        results[endpoint] = {"status": status, "data": data}
                    else:
                        results[endpoint] = {"status": status, "data": None}
            except Exception as e:
                results[endpoint] = {"status": "error", "error": str(e)}
    
    return results
