import asyncio
import json
from utils import test_endpoints

async def main():
    uid = "2003919727"
    region = "india"
    
    print(f"Testing endpoints for UID: {uid}, Region: {region}")
    results = await test_endpoints(uid, region)
    
    # Pretty print the results
    for endpoint, result in results.items():
        print(f"\nEndpoint: {endpoint}")
        print(f"Status: {result['status']}")
        
        if result.get('error'):
            print(f"Error: {result['error']}")
        elif result.get('data'):
            # Format the JSON data for better readability
            formatted_data = json.dumps(result['data'], indent=2)
            print(f"Data: {formatted_data}")
        else:
            print("No data returned")
        print("-" * 50)

if __name__ == "__main__":
    asyncio.run(main())